{"extName": {"message": "FindSomething", "description": "اسم الإضافة"}, "extDescription": {"message": "العثور على أشياء مثيرة للاهتمام في كود المصدر أو JavaScript للصفحة", "description": "وصف الإضافة"}, "Zhuye": {"message": "الرئيسية", "description": "الصفحة الرئيسية للنافذة المنبثقة"}, "Peizhi": {"message": "الإعدادات", "description": "صفحة الإعدادات للنافذة المنبثقة"}, "popupCopy": {"message": "نسخ", "description": "زر النسخ في الصفحة المنبثقة"}, "popupCopyurl": {"message": "نسخ الرابط", "description": "زر نسخ الرابط في الصفحة المنبثقة"}, "popupIp": {"message": "الآي بي", "description": "عنوان في الصفحة المنبثقة: الآي بي"}, "popupIpPort": {"message": "الآي بي والمنفذ", "description": "عنوان في الصفحة المنبثقة: الآي بي والمنفذ"}, "popupDomain": {"message": "النطاق", "description": "عنوان في الصفحة المنبثقة: النطاق"}, "popupSfz": {"message": "بطاقة الهوية", "description": "عنوان في الصفحة المنبثقة: بطاقة الهوية"}, "popupMobile": {"message": "رقم الهات<PERSON> المحمول", "description": "عنوان في الصفحة المنبثقة: رقم الهاتف المحمول"}, "popupMail": {"message": "الب<PERSON>يد الإلكتروني", "description": "عنوان في الصفحة المنبثقة: الب<PERSON>ي<PERSON> الإلكتروني"}, "popupJwt": {"message": "JWT", "description": "عنوان في الصفحة المنبثقة: JWT"}, "popupAlgorithm": {"message": "الخوارزمية", "description": "عنوان في الصفحة المنبثقة: الخوارزمية"}, "popupSecret": {"message": "معلومات حساسة", "description": "عنوان في الصفحة المنبثقة: معلومات حساسة"}, "popupPath": {"message": "المسار", "description": "عنوان في الصفحة المنبثقة: المسار"}, "popupIncompletePath": {"message": "مسار غير كامل", "description": "عنوان في الصفحة المنبثقة: مسار غير كامل"}, "popupUrl": {"message": "الرابط", "description": "عنوان في الصفحة المنبثقة: الرابط"}, "popupStaticPath": {"message": "مسار ثابت", "description": "عنوان في الصفحة المنبثقة: مسار ثابت"}, "popupProcessing": {"message": "جاري المعالجة...", "description": "تلميح التقدم في الصفحة المنبثقة: جاري المعالجة"}, "popupComplete": {"message": "اكتملت المعالجة:", "description": "تلميح التقدم في الصفحة المنبثقة: اكتملت المعالجة"}, "popupTipClickBeforeCopy": {"message": "الرجاء النقر على الصفحة الأصلية قبل النسخ :)", "description": "تلميح النسخ في الصفحة المنبثقة عند حدوث خطأ"}, "settingClearCache": {"message": "مس<PERSON> الذاكرة المؤقتة", "description": "عنوان في صفحة الإعدادات: مسح الذاكرة المؤقتة"}, "settingClearLocalStorage": {"message": "م<PERSON><PERSON>", "description": "زر في صفحة الإعدادات: مسح"}, "settingClearComplete": {"message": "اكتمل المسح", "description": "تلميح في صفحة الإعدادات: اكتمل المسح"}, "settingClosed": {"message": "مغلق", "description": "زر في صفحة الإعدادات: مغلق"}, "settingOpened": {"message": "مفتوح", "description": "زر في صفحة الإعدادات: مفتوح"}, "settingGlobalFloatingWindow": {"message": "نافذة عائمة عالمية", "description": "عنوان في صفحة الإعدادات: نافذة عائمة عالمية"}, "settingAutoTimeout": {"message": "المهلة التلقائية", "description": "عنوان في صفحة الإعدادات: المهلة التلقائية"}, "settingSafe": {"message": "الوضع الآمن", "description": "عنوان في صفحة الإعدادات: الوضع الآمن"}, "settingWebhook": {"message": "Webhook", "description": "عنوان في صفحة الإعدادات: Webhook"}, "settingWebhookUrl": {"message": "عنوان URL للرد", "description": "عنصر الإعداد في صفحة الإعدادات: عنوان URL للرد"}, "settingWebhookMethod": {"message": "طريقة الطلب", "description": "عنصر الإعداد في صفحة الإعدادات: طريقة الطلب"}, "settingWebhookArg": {"message": "معاملات الطلب", "description": "عنصر الإعداد في صفحة الإعدادات: معاملات الطلب"}, "settingWebhookHeaders": {"message": "رؤوس مخصصة", "description": "عنصر الإعداد في صفحة الإعدادات: رؤوس مخصصة"}, "settingDomainAllowList": {"message": "قائمة السماح للنطاقات", "description": "عنصر الإعداد في صفحة الإعدادات: قائمة السماح للنطاقات"}, "settingDomainAllowListTip": {"message": "أدخل الجزء النهائي من النطاق، مفصولاً بسطور جديدة، إذا لم يتم التكوين يستخدم الافتراضي .google.com، .amazon.com، portswigger.net", "description": "تلميح في صفحة الإعدادات لقائمة السماح للنطاقات"}, "settingResetAndSave": {"message": "إعادة تعيين وحفظ", "description": "زر في صفحة الإعدادات: إعادة تعيين وحفظ"}, "settingSave": {"message": "<PERSON><PERSON><PERSON>", "description": "زر في صفحة الإعدادات: حفظ"}, "getstartedContent": {"message": "كيف يمكنك استخدامه بشكل جيد إذا لم تفهم الميزات؟\n## ميزات الصفحة الرئيسية\n* مطابقة وعرض الحقول للـ IP، IP+port، النطاق، رقم بطاقة الهوية، رقم الهاتف، البريد الإلكتروني، JWT، خوارزميات التشفير، المعلومات الحساسة، المسار، المسار غير الكامل، الـ URL الكامل، المسار الثابت، إلخ.\n* النسخ\n    * زر نسخ لكل منطقة، يمكنه نسخ محتوى المنطقة المقابلة\n    * زر \"نسخ الـ URL\" منفصل يمكنه نسخ محتوى منطقة الـ PATH وإضافة URL الصفحة الحالية إلى كل سطر لتشكيل URL كامل.\n* مصدر المعلومات\n    * كل معلومة معروضة يسبقها '<' برتقالي. ضع الماوس فوقه وانتظر لمدة 2 ثانية لرؤية URL مصدر المعلومة الحالية. يمكنك أيضًا فتح رابط المصدر بـ \"ctrl+click\" أو \"بزر اليمين - فتح الرابط في علامة تبويب جديدة\".\n* عرض التقدم\n    * يتم عرض التقدم في المعالجة أسفل صفحة \"الرئيسية\"، على سبيل المثال، العملية: 1/20، حيث 1 هو عدد الروابط المطلوبة و20 هو العدد الإجمالي للروابط المطلوبة. إذا تم إكمال جميع الطلبات، سيتم عرضها كمكتملة: 20/20.\n    * منطق تحديث الصفحة بأكملها يعتمد أيضًا على تقدم المعالجة، وتحديث البيانات عن طريق الاستماع إلى تغييرات البيانات قبل اكتمال المعالجة.\n## ميزات صفحة الإعدادات\n* مسح الذاكرة المؤقتة\n    * نظرًا لأن البيانات المستخرجة مخزنة في الـ localstorage الخاص بالمتصفح، فقد تشغل ذاكرة إذا لم يتم مسحها لفترة طويلة.\n    * أدخل البرنامج المساعد منطق انتهاء الصلاحية التلقائي في الإصدار 2.0.17، حيث ستنتهي صلاحية البيانات التي لم يتم الوصول إليها خلال 7 أيام.\n    * يمكن للمستخدمين أيضًا النقر يدويًا على \"مسح\" لمسح البيانات المخزنة على الفور.\n* النافذة العائمة العالمية\n    * بمجرد فتحها، ستدمج صفحة FindSomething في كل صفحة مفتوحة، مما يلغي الحاجة إلى التفاعل مع البرنامج المساعد.\n    * ومع ذلك، لم تتم إضافة ميزات جديدة إلى النافذة العائمة العالمية بعد، فقط الحفاظ على الوظائف الأساسية.\n* التوقيت الآلي\n    * هذا الإعداد مغلق بشكل افتراضي. عند تمكينه، سيتم إنهاء الطلبات التي بدأها البرنامج المساعد في الخلفية بعد 2 ثانية لتجنب أوقات الانتظار الطويلة.\n* الوضع الآمن\n    * في الوضع الآمن، سيقوم البرنامج المساعد بالوصول إلى موارد JavaScrip", "description": "ابد<PERSON>"}}