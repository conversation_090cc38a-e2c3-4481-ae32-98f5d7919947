package utils

import (
	"net"
	"strconv"
	"strings"
)

// FilterUtils 过滤和验证工具类 - 统一所有过滤逻辑
// 功能：数据验证、内容过滤、格式检查等
type FilterUtils struct {
	regexManager *RegexManager // 正则表达式管理器
	stringUtils  *StringUtils  // 字符串工具
}

// 全局过滤工具实例
var GlobalFilterUtils = NewFilterUtils()

// NewFilterUtils 创建新的过滤工具实例
func NewFilterUtils() *FilterUtils {
	return &FilterUtils{
		regexManager: GetRegexManager(),
		stringUtils:  GlobalStringUtils,
	}
}

// === 敏感信息验证方法 ===

// IsValidSFZ 严格验证身份证号
func (fu *FilterUtils) IsValidSFZ(sfz string) bool {
	// 长度检查
	if len(sfz) != 15 && len(sfz) != 18 {
		return false
	}

	// 18位身份证验证
	if len(sfz) == 18 {
		// 前17位必须是数字
		for i := 0; i < 17; i++ {
			if sfz[i] < '0' || sfz[i] > '9' {
				return false
			}
		}

		// 最后一位可以是数字或X
		lastChar := sfz[17]
		if lastChar != 'X' && lastChar != 'x' && (lastChar < '0' || lastChar > '9') {
			return false
		}

		// 验证出生日期
		year, _ := strconv.Atoi(sfz[6:10])
		month, _ := strconv.Atoi(sfz[10:12])
		day, _ := strconv.Atoi(sfz[12:14])

		if year < 1900 || year > 2030 || month < 1 || month > 12 || day < 1 || day > 31 {
			return false
		}

		return true
	}

	// 15位身份证验证
	if len(sfz) == 15 {
		// 全部必须是数字
		for _, char := range sfz {
			if char < '0' || char > '9' {
				return false
			}
		}

		// 验证出生日期（15位身份证年份只有2位，需要推断世纪）
		year, _ := strconv.Atoi(sfz[6:8])
		month, _ := strconv.Atoi(sfz[8:10])
		day, _ := strconv.Atoi(sfz[10:12])

		// 假设00-30为20xx年，31-99为19xx年
		if year <= 30 {
			year += 2000
		} else {
			year += 1900
		}

		if year < 1900 || year > 2030 || month < 1 || month > 12 || day < 1 || day > 31 {
			return false
		}

		return true
	}

	return false
}

// IsValidPhone 验证电话号码格式（手机号、400热线、固定电话）
func (fu *FilterUtils) IsValidPhone(phone string) bool {
	// 清理电话号码
	cleanPhone := fu.stringUtils.CleanPhoneNumber(phone)

	// 手机号验证（11位，1开头）
	if len(cleanPhone) == 11 && cleanPhone[0] == '1' {
		// 第二位必须是3-9
		if cleanPhone[1] >= '3' && cleanPhone[1] <= '9' {
			// 其余位必须是数字
			for i := 2; i < 11; i++ {
				if cleanPhone[i] < '0' || cleanPhone[i] > '9' {
					return false
				}
			}
			return true
		}
	}

	// 400客服热线验证
	if strings.HasPrefix(cleanPhone, "400") && len(cleanPhone) == 10 {
		// 其余位必须是数字
		for i := 3; i < 10; i++ {
			if cleanPhone[i] < '0' || cleanPhone[i] > '9' {
				return false
			}
		}
		return true
	}

	// 固定电话验证（区号+号码，总长度10-12位）
	if len(cleanPhone) >= 10 && len(cleanPhone) <= 12 && cleanPhone[0] == '0' {
		// 全部必须是数字
		for _, char := range cleanPhone {
			if char < '0' || char > '9' {
				return false
			}
		}
		return true
	}

	return false
}

// IsValidPhoneWithContext 带上下文验证的电话号码验证
func (fu *FilterUtils) IsValidPhoneWithContext(phone string, sourceData string) bool {
	// 首先进行基本格式验证
	if !fu.IsValidPhone(phone) {
		return false
	}

	// 进行上下文关键词验证，减少误报
	return fu.hasPhoneContextKeywords(phone, sourceData)
}

// hasPhoneContextKeywords 检查电话号码周围是否有相关关键词
func (fu *FilterUtils) hasPhoneContextKeywords(phone string, sourceData string) bool {
	// 基本关键词列表
	basicKeywords := []string{
		"电话", "手机", "联系", "热线", "客服", "咨询", "服务",
		"phone", "mobile", "tel", "contact", "hotline", "service",
		"call", "number", "客户", "support", "help",
	}

	// 查找电话号码在源数据中的位置
	phoneIndex := strings.Index(sourceData, phone)
	if phoneIndex == -1 {
		return false
	}

	// 提取上下文（前后各200个字符）
	start := phoneIndex - 200
	if start < 0 {
		start = 0
	}

	end := phoneIndex + len(phone) + 200
	if end > len(sourceData) {
		end = len(sourceData)
	}

	context := strings.ToLower(sourceData[start:end])

	// 检查上下文中是否包含基本关键词
	for _, keyword := range basicKeywords {
		if strings.Contains(context, strings.ToLower(keyword)) {
			return true
		}
	}

	// 检查是否在HTML或JSON结构中（更宽松的检查）
	structuralPatterns := []string{
		"<", ">", "\"", "'", ":", "=", "{", "}", "[", "]",
	}

	structuralCount := 0
	for _, pattern := range structuralPatterns {
		if strings.Contains(context, pattern) {
			structuralCount++
		}
	}

	// 如果在结构化数据中，认为是有效的
	return structuralCount >= 2
}

// IsValidEmail 验证邮箱地址格式
func (fu *FilterUtils) IsValidEmail(email string) bool {
	email = fu.stringUtils.CleanEmail(email)

	// 基本格式检查
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return false
	}

	// 长度检查
	if len(email) < 5 || len(email) > 254 {
		return false
	}

	// 分割用户名和域名
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	username, domain := parts[0], parts[1]

	// 用户名验证
	if len(username) == 0 || len(username) > 64 {
		return false
	}

	// 域名验证
	if !fu.stringUtils.IsValidDomain(domain) {
		return false
	}

	return true
}

// === IP地址验证方法 ===

// IsValidIP 验证IP地址格式
func (fu *FilterUtils) IsValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// IsPrivateIP 检查是否为私有IP地址
func (fu *FilterUtils) IsPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查私有IP地址范围
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8",
		"***********/16",
		"*********/4",
		"240.0.0.0/4",
	}

	for _, cidr := range privateRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}

		if network.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// IsPublicIP 检查是否为公网IP地址
func (fu *FilterUtils) IsPublicIP(ip string) bool {
	return fu.IsValidIP(ip) && !fu.IsPrivateIP(ip)
}

// === 域名验证方法 ===

// IsValidDomain 验证域名格式和有效性
func (fu *FilterUtils) IsValidDomain(domain string) bool {
	domain = fu.stringUtils.CleanDomain(domain)

	// 基本格式验证
	if !fu.stringUtils.IsValidDomain(domain) {
		return false
	}

	// 检查是否包含大写字母（过滤规则）
	if strings.ToLower(domain) != domain {
		return false
	}

	// 检查是否为单字符主域名（如a.com）
	parts := strings.Split(domain, ".")
	if len(parts) >= 2 {
		mainDomain := parts[len(parts)-2]
		if len(mainDomain) == 1 {
			return false
		}
	}

	// 检查顶级域名
	if len(parts) < 2 {
		return false
	}

	tld := parts[len(parts)-1]
	if len(tld) < 2 {
		return false
	}

	return true
}

// IsJavaScriptProperty 检查是否为JavaScript对象属性访问
func (fu *FilterUtils) IsJavaScriptProperty(domain string) bool {
	pattern := fu.regexManager.GetPattern("js_property")
	if pattern == nil {
		return false
	}

	return pattern.MatchString(domain)
}

// === URL和路径验证方法 ===

// IsValidURLCandidate 验证URL候选项
func (fu *FilterUtils) IsValidURLCandidate(url string) bool {
	url = fu.stringUtils.CleanURL(url)

	// 基本URL格式验证
	if !fu.stringUtils.IsValidURL(url) {
		return false
	}

	// 检查是否为测试数据
	if fu.isTestData(url) {
		return false
	}

	// 检查文件扩展名（排除静态资源）
	if fu.isStaticResource(url) {
		return false
	}

	return true
}

// IsValidPathCandidate 验证路径候选项
func (fu *FilterUtils) IsValidPathCandidate(path string) bool {
	path = fu.stringUtils.CleanPath(path)

	// 基本路径格式验证
	if !fu.stringUtils.IsValidPath(path) {
		return false
	}

	// 检查是否为测试数据
	if fu.isTestData(path) {
		return false
	}

	return true
}

// IsValidJSPath 验证从JavaScript文件中提取的路径
// 优化版本：按照新思路进行路径验证，减少过度过滤，保证提取完整性
func (fu *FilterUtils) IsValidJSPath(path string) bool {
	if path == "" {
		return false
	}

	// 基本长度检查（避免过短或过长的无效路径）
	if len(path) < 1 || len(path) > 2048 {
		return false
	}

	// 排除HTML标签和特殊协议
	if strings.Contains(path, "<") || strings.Contains(path, ">") ||
		strings.Contains(path, "javascript:") || strings.Contains(path, "data:") ||
		strings.Contains(path, "mailto:") || strings.Contains(path, "tel:") {
		return false
	}

	// 排除明显的代码片段（保持基本的噪音过滤）
	obviousCodePatterns := []string{
		"function(", "return ", "console.", "document.", "window.",
		"var ", "let ", "const ", "if(", "for(", "while(",
		");", "};", "({", "})",
	}

	for _, pattern := range obviousCodePatterns {
		if strings.Contains(path, pattern) {
			return false
		}
	}

	// 排除明显的CSS选择器模式
	if strings.HasPrefix(path, ".") && strings.Contains(path, "{") {
		return false
	}

	// 其他验证都放宽，保证路径提取的完整性
	// 移除了过于严格的验证条件，如：
	// - 纯数字路径检查（某些API可能使用数字路径）
	// - 单字符路径检查（某些短路径可能有效）
	// - HTML标签检查（可能误杀有效路径）
	// - 测试数据检查（可能过于严格）

	return true
}

// === 密钥验证方法 ===

// IsValidSecret 验证密钥信息
func (fu *FilterUtils) IsValidSecret(secret string, secretType string) bool {
	// 基本长度检查
	if len(secret) < 10 {
		return false
	}

	switch secretType {
	case "api_key":
		// API密钥通常是字母数字组合，长度20-128位
		return len(secret) >= 20 && len(secret) <= 128 && fu.stringUtils.IsAlphaNumeric(secret)
	case "aws_key":
		// AWS密钥格式：AKIA开头，20位字母数字
		return len(secret) == 20 && strings.HasPrefix(secret, "AKIA")
	case "jwt":
		// JWT格式检查：三个部分用.分隔
		parts := strings.Split(secret, ".")
		return len(parts) == 3 && len(parts[0]) > 0 && len(parts[1]) > 0 && len(parts[2]) > 0
	default:
		return len(secret) >= 20 // 默认最小长度
	}
}

// IsObviousNonSecret 检查是否明显不是密钥
func (fu *FilterUtils) IsObviousNonSecret(value string) bool {
	// 检查是否包含明显的非密钥模式
	nonSecretPatterns := []string{
		"function", "var", "let", "const", "return", "if", "else",
		"true", "false", "null", "undefined", "console", "document",
		"window", "this", "prototype", "length", "indexOf", "toString",
	}

	lowerValue := strings.ToLower(value)
	for _, pattern := range nonSecretPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}

	return false
}

// === 内部辅助方法 ===

// isTestData 检查是否为测试数据
func (fu *FilterUtils) isTestData(str string) bool {
	pattern := fu.regexManager.GetPattern("test_data")
	if pattern == nil {
		return false
	}

	return pattern.MatchString(str)
}

// isStaticResource 检查是否为静态资源
func (fu *FilterUtils) isStaticResource(url string) bool {
	pattern := fu.regexManager.GetPattern("file_extension")
	if pattern == nil {
		return false
	}

	return pattern.MatchString(url)
}

// isFilePattern 检查是否包含典型的文件名模式
func (fu *FilterUtils) isFilePattern(str string) bool {
	filePatterns := []string{
		"index.", "main.", "app.", "script.", "style.", "config.", "settings.",
		"readme.", "license.", "changelog.", "package.", "manifest.",
	}

	lowerStr := strings.ToLower(str)
	for _, pattern := range filePatterns {
		if strings.Contains(lowerStr, pattern) {
			return true
		}
	}

	return false
}
