package utils

import (
	"net/url"
	"strings"
	"unicode"
)

// StringUtils 字符串处理工具类 - 统一所有字符串处理逻辑
// 功能：清理、验证、格式化、转换等字符串操作
type StringUtils struct{}

// 全局字符串工具实例
var GlobalStringUtils = &StringUtils{}

// === URL处理相关方法 ===

// NormalizeURL 标准化URL，确保一致性并减少重复
// 功能说明：
//   - 统一协议格式（默认添加http://）
//   - 移除尾部斜杠（除根路径外）
//   - 转换为小写（域名部分）
//   - 移除默认端口号
func (su *StringUtils) NormalizeURL(rawURL string) string {
	// 如果没有协议，默认添加http://
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "http://" + rawURL
	}

	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		// 如果解析失败，返回原始URL
		return rawURL
	}

	// 标准化主机名（转小写）
	parsedURL.Host = strings.ToLower(parsedURL.Host)

	// 移除默认端口
	if parsedURL.Scheme == "http" && strings.HasSuffix(parsedURL.Host, ":80") {
		parsedURL.Host = strings.TrimSuffix(parsedURL.Host, ":80")
	} else if parsedURL.Scheme == "https" && strings.HasSuffix(parsedURL.Host, ":443") {
		parsedURL.Host = strings.TrimSuffix(parsedURL.Host, ":443")
	}

	// 移除尾部斜杠（除非是根路径）
	if parsedURL.Path != "/" && strings.HasSuffix(parsedURL.Path, "/") {
		parsedURL.Path = strings.TrimSuffix(parsedURL.Path, "/")
	}

	return parsedURL.String()
}

// CleanURL 清理URL，移除多余字符和格式化
func (su *StringUtils) CleanURL(url string) string {
	if url == "" {
		return ""
	}

	// 移除引号和空白字符
	url = strings.Trim(url, "'\"` \t\n\r")

	// 移除末尾的特殊字符
	url = strings.TrimRight(url, ",;)}")

	// 处理转义字符
	url = strings.ReplaceAll(url, "\\/", "/")

	// 移除URL片段标识符（#后面的部分）
	if idx := strings.Index(url, "#"); idx != -1 {
		url = url[:idx]
	}

	// 移除多余的查询参数分隔符
	url = strings.TrimRight(url, "&?")

	return url
}

func (su *StringUtils) CleanPath(path string) string {
	if path == "" {
		return ""
	}

	path = su.cleanCommonChars(path)
	path = strings.TrimRight(path, ",;)}")
	path = strings.ReplaceAll(path, "\\/", "/")

	if !strings.HasPrefix(path, "/") && path != "" {
		path = "/" + path
	}

	return path
}

// CleanDomain 清理域名，移除协议和路径部分
func (su *StringUtils) CleanDomain(domain string) string {
	if domain == "" {
		return ""
	}

	// 移除协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")
	domain = strings.TrimPrefix(domain, "//")

	// 移除路径部分
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}

	// 移除端口号
	if idx := strings.Index(domain, ":"); idx != -1 {
		domain = domain[:idx]
	}

	// 转换为小写
	domain = strings.ToLower(domain)

	return strings.TrimSpace(domain)
}

// === 敏感信息处理相关方法 ===

func (su *StringUtils) CleanSensitiveValue(value string) string {
	if value == "" {
		return ""
	}

	value = su.cleanCommonChars(value)
	value = su.cleanHTMLEntities(value)

	return value
}

// cleanCommonChars 清理常见的引号和空白字符
// 这是一个内部辅助方法，用于统一处理字符串清理逻辑
func (su *StringUtils) cleanCommonChars(s string) string {
	return strings.Trim(s, "'\"` \t\n\r")
}

// cleanHTMLEntities 清理HTML实体字符
// 移除常见的HTML实体编码，返回纯文本
func (su *StringUtils) cleanHTMLEntities(s string) string {
	replacements := map[string]string{
		"&quot;": "",
		"&apos;": "",
		"&lt;":   "",
		"&gt;":   "",
		"&amp;":  "",
	}

	for old, new := range replacements {
		s = strings.ReplaceAll(s, old, new)
	}

	return s
}

// CleanPhoneNumber 清理电话号码，移除分隔符
func (su *StringUtils) CleanPhoneNumber(phone string) string {
	if phone == "" {
		return ""
	}

	// 移除常见分隔符
	phone = strings.ReplaceAll(phone, "-", "")
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "(", "")
	phone = strings.ReplaceAll(phone, ")", "")

	return strings.TrimSpace(phone)
}

// CleanEmail 清理邮箱地址
func (su *StringUtils) CleanEmail(email string) string {
	if email == "" {
		return ""
	}

	// 转换为小写
	email = strings.ToLower(email)

	// 移除空白字符
	email = strings.TrimSpace(email)

	return email
}

// === 内容处理相关方法 ===

// RemoveComments 移除JavaScript/CSS注释
func (su *StringUtils) RemoveComments(content string) string {
	if content == "" {
		return ""
	}

	// 移除单行注释
	lines := strings.Split(content, "\n")
	var cleanLines []string

	for _, line := range lines {
		// 移除//注释
		if idx := strings.Index(line, "//"); idx != -1 {
			line = line[:idx]
		}
		cleanLines = append(cleanLines, line)
	}

	content = strings.Join(cleanLines, "\n")

	// 移除多行注释 /* ... */
	for {
		start := strings.Index(content, "/*")
		if start == -1 {
			break
		}

		end := strings.Index(content[start:], "*/")
		if end == -1 {
			break
		}

		content = content[:start] + content[start+end+2:]
	}

	return content
}

// RemoveStrings 移除字符串字面量
func (su *StringUtils) RemoveStrings(content string) string {
	if content == "" {
		return ""
	}

	// 移除双引号字符串
	content = su.removeQuotedStrings(content, '"')

	// 移除单引号字符串
	content = su.removeQuotedStrings(content, '\'')

	// 移除反引号字符串
	content = su.removeQuotedStrings(content, '`')

	return content
}

// removeQuotedStrings 移除指定引号包围的字符串
func (su *StringUtils) removeQuotedStrings(content string, quote rune) string {
	var result strings.Builder
	inString := false
	escaped := false

	for _, char := range content {
		if escaped {
			escaped = false
			if inString {
				result.WriteRune(' ') // 用空格替换字符串内容
			} else {
				result.WriteRune(char)
			}
			continue
		}

		if char == '\\' {
			escaped = true
			if !inString {
				result.WriteRune(char)
			}
			continue
		}

		if char == quote {
			if inString {
				inString = false
				result.WriteRune(' ') // 用空格替换结束引号
			} else {
				inString = true
				result.WriteRune(' ') // 用空格替换开始引号
			}
			continue
		}

		if inString {
			result.WriteRune(' ') // 用空格替换字符串内容
		} else {
			result.WriteRune(char)
		}
	}

	return result.String()
}

// === 验证相关方法 ===

// IsValidURL 验证URL格式
func (su *StringUtils) IsValidURL(url string) bool {
	if url == "" {
		return false
	}

	// 基本长度检查
	if len(url) < 7 || len(url) > 2048 {
		return false
	}

	// 协议检查
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return false
	}

	// 不能包含空格
	if strings.Contains(url, " ") {
		return false
	}

	return true
}

// IsJavaScriptFile 检测URL是否为JavaScript文件
// 用于确定path字段是否应该从该source中提取
func (su *StringUtils) IsJavaScriptFile(url string) bool {
	if url == "" {
		return false
	}

	// 转换为小写进行检查
	lowerURL := strings.ToLower(url)

	// 检查文件扩展名：以.js结尾
	if strings.HasSuffix(lowerURL, ".js") {
		return true
	}

	// 检查带参数的JS文件：包含.js?
	if strings.Contains(lowerURL, ".js?") {
		return true
	}

	// 检查JS目录路径：包含/js/
	if strings.Contains(lowerURL, "/js/") {
		return true
	}

	// 检查常见的JS文件模式
	jsPatterns := []string{
		"/javascript/", "/scripts/", "/assets/js/", "/static/js/",
		".min.js", ".bundle.js", ".chunk.js", ".vendor.js",
	}

	for _, pattern := range jsPatterns {
		if strings.Contains(lowerURL, pattern) {
			return true
		}
	}

	return false
}

// IsValidPath 验证路径格式
func (su *StringUtils) IsValidPath(path string) bool {
	if path == "" {
		return false
	}

	// 必须以/开头
	if !strings.HasPrefix(path, "/") {
		return false
	}

	// 基本长度检查
	if len(path) > 1024 {
		return false
	}

	// 不能包含空格
	if strings.Contains(path, " ") {
		return false
	}

	return true
}

// IsValidDomain 验证域名格式
func (su *StringUtils) IsValidDomain(domain string) bool {
	if domain == "" {
		return false
	}

	// 长度检查
	if len(domain) < 4 || len(domain) > 253 {
		return false
	}

	// 不能包含协议
	if strings.Contains(domain, "://") {
		return false
	}

	// 必须包含点号
	if !strings.Contains(domain, ".") {
		return false
	}

	// 不能以点号开头或结尾
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	return true
}

// IsAlphaNumeric 检查字符串是否只包含字母和数字
func (su *StringUtils) IsAlphaNumeric(s string) bool {
	for _, char := range s {
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) {
			return false
		}
	}
	return true
}

// === 格式化相关方法 ===

// NormalizeWhitespace 标准化空白字符
func (su *StringUtils) NormalizeWhitespace(s string) string {
	// 将所有空白字符替换为单个空格
	fields := strings.Fields(s)
	return strings.Join(fields, " ")
}

// TruncateString 截断字符串到指定长度
func (su *StringUtils) TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}

	if maxLen <= 3 {
		return s[:maxLen]
	}

	return s[:maxLen-3] + "..."
}

// === 转换相关方法 ===

// ToSnakeCase 转换为蛇形命名
func (su *StringUtils) ToSnakeCase(s string) string {
	var result strings.Builder

	for i, char := range s {
		if unicode.IsUpper(char) && i > 0 {
			result.WriteRune('_')
		}
		result.WriteRune(unicode.ToLower(char))
	}

	return result.String()
}

// ToCamelCase 转换为驼峰命名
func (su *StringUtils) ToCamelCase(s string) string {
	words := strings.Split(s, "_")
	if len(words) == 0 {
		return s
	}

	result := strings.ToLower(words[0])
	for i := 1; i < len(words); i++ {
		if len(words[i]) > 0 {
			result += strings.ToUpper(words[i][:1]) + strings.ToLower(words[i][1:])
		}
	}

	return result
}
