package utils

// MergeUtils 合并工具类 - 统一所有数据合并逻辑
// 功能：敏感信息合并、资源合并、结果合并等
type MergeUtils struct{}

// 全局合并工具实例
var GlobalMergeUtils = &MergeUtils{}

// MergeInfoItems 合并信息项列表并去重
// 参数：
//   - existing: 现有的信息项列表
//   - new: 新的信息项列表
//
// 返回值：
//   - []InfoItem: 合并并去重后的信息项列表
func (mu *MergeUtils) MergeInfoItems(existing, new []InfoItem) []InfoItem {
	if len(existing) == 0 {
		return new
	}

	if len(new) == 0 {
		return existing
	}

	// 使用map进行去重
	seen := make(map[string]bool)
	var result []InfoItem

	// 添加现有项
	for _, item := range existing {
		key := item.Value + "|" + item.Source
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}

	// 添加新项
	for _, item := range new {
		key := item.Value + "|" + item.Source
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}

	return result
}
