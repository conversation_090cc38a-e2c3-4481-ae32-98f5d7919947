package utils

import (
	"regexp"
	"strings"
)

// InfoItem 信息项结构 - utils包版本
type InfoItem struct {
	Value  string `json:"value"`
	Source string `json:"source"`
}

// ExtractorUtils 提取器工具类 - 统一所有提取策略
// 功能：敏感信息提取、URL/路径提取、API提取等
type ExtractorUtils struct {
	regexManager *RegexManager // 正则表达式管理器
	filterUtils  *FilterUtils  // 过滤工具
	stringUtils  *StringUtils  // 字符串工具
}

// 全局提取器工具实例
var GlobalExtractorUtils = NewExtractorUtils()

// NewExtractorUtils 创建新的提取器工具实例
func NewExtractorUtils() *ExtractorUtils {
	return &ExtractorUtils{
		regexManager: GetRegexManager(),
		filterUtils:  GlobalFilterUtils,
		stringUtils:  GlobalStringUtils,
	}
}

// === 敏感信息提取方法 ===

// ExtractSFZ 提取身份证号码
func (eu *ExtractorUtils) ExtractSFZ(content string, source string) []InfoItem {
	pattern := eu.regexManager.GetPattern("sfz")
	if pattern == nil {
		return nil
	}

	var results []InfoItem
	matches := pattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			value := eu.stringUtils.CleanSensitiveValue(match[1])
			if eu.filterUtils.IsValidSFZ(value) {
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// ExtractPhones 提取电话号码（手机号、400热线、固定电话）
func (eu *ExtractorUtils) ExtractPhones(content string, source string) []InfoItem {
	var results []InfoItem

	// 提取手机号
	mobilePattern := eu.regexManager.GetPattern("phone_mobile")
	if mobilePattern != nil {
		matches := mobilePattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				value := eu.stringUtils.CleanPhoneNumber(match[1])
				if eu.filterUtils.IsValidPhoneWithContext(value, content) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}
	}

	// 提取400客服热线
	hotlinePattern := eu.regexManager.GetPattern("phone_400")
	if hotlinePattern != nil {
		matches := hotlinePattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				value := eu.stringUtils.CleanPhoneNumber(match[1])
				if eu.filterUtils.IsValidPhoneWithContext(value, content) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}
	}

	// 提取固定电话
	fixedPattern := eu.regexManager.GetPattern("phone_fixed")
	if fixedPattern != nil {
		matches := fixedPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				value := eu.stringUtils.CleanPhoneNumber(match[1])
				if eu.filterUtils.IsValidPhoneWithContext(value, content) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// ExtractEmails 提取邮箱地址
func (eu *ExtractorUtils) ExtractEmails(content string, source string) []InfoItem {
	pattern := eu.regexManager.GetPattern("email")
	if pattern == nil {
		return nil
	}

	var results []InfoItem
	matches := pattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			value := eu.stringUtils.CleanEmail(match[1])
			if eu.filterUtils.IsValidEmail(value) {
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// ExtractIPs 提取IP地址
func (eu *ExtractorUtils) ExtractIPs(content string, source string) []InfoItem {
	pattern := eu.regexManager.GetPattern("ipv4")
	if pattern == nil {
		return nil
	}

	var results []InfoItem
	matches := pattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			value := strings.TrimSpace(match[1])
			if eu.filterUtils.IsPublicIP(value) { // 只保留公网IP
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// ExtractDomains 提取域名
func (eu *ExtractorUtils) ExtractDomains(content string, source string) []InfoItem {
	pattern := eu.regexManager.GetPattern("domain")
	if pattern == nil {
		return nil
	}

	var results []InfoItem
	matches := pattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			value := eu.stringUtils.CleanDomain(match[1])
			if eu.filterUtils.IsValidDomain(value) && !eu.filterUtils.IsJavaScriptProperty(value) {
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// === URL和路径提取方法 ===

// ExtractURLs 提取URL
func (eu *ExtractorUtils) ExtractURLs(content string, source string) []InfoItem {
	pattern := eu.regexManager.GetPattern("url_http")
	if pattern == nil {
		return nil
	}

	var results []InfoItem
	matches := pattern.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			value := eu.stringUtils.CleanURL(match[1])
			if eu.filterUtils.IsValidURLCandidate(value) {
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// ExtractPaths 提取路径 - 按照新思路优化
// 重要：path字段从JavaScript文件和HTML中的JavaScript代码中提取，统一归类为path（不区分完整和不完整）
func (eu *ExtractorUtils) ExtractPaths(content string, source string) []InfoItem {
	var results []InfoItem

	// 检查source是否为JavaScript文件，或者是HTML文件（可能包含JavaScript代码）
	isJSFile := eu.stringUtils.IsJavaScriptFile(source)
	isHTMLFile := strings.Contains(strings.ToLower(source), ".html") || strings.Contains(content, "<script")

	if !isJSFile && !isHTMLFile {
		// 如果既不是JS文件也不是HTML文件，不提取path
		return results
	}

	// 如果是HTML文件，先提取其中的JavaScript代码
	var jsContent string
	if isHTMLFile && !isJSFile {
		jsContent = eu.extractJavaScriptFromHTML(content)
	} else {
		jsContent = content
	}

	if jsContent == "" {
		return results
	}

	// 获取JavaScript专用路径提取模式
	jsPathPatterns := eu.regexManager.GetJSPathPatterns()

	// 使用所有JS路径模式进行提取
	for patternName, pattern := range jsPathPatterns {
		if pattern == nil {
			continue
		}

		matches := pattern.FindAllStringSubmatch(jsContent, -1)
		for _, match := range matches {
			if len(match) > 1 {
				// 使用捕获组中的路径内容
				extractedPath := match[1]

				// 清理路径
				value := eu.stringUtils.CleanPath(extractedPath)

				// 使用JS专用路径验证，进行合理降噪
				if eu.filterUtils.IsValidJSPath(value) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}

		// 记录提取来源（用于调试）
		_ = patternName
	}

	return eu.uniqueInfoItems(results)
}

// extractJavaScriptFromHTML 从HTML内容中提取JavaScript代码
func (eu *ExtractorUtils) extractJavaScriptFromHTML(htmlContent string) string {
	// 使用正则表达式提取<script>标签中的内容
	// (?is) 表示忽略大小写且让.匹配换行符
	scriptPattern := regexp.MustCompile(`(?is)<script[^>]*>(.*?)</script>`)
	matches := scriptPattern.FindAllStringSubmatch(htmlContent, -1)

	var jsCode strings.Builder
	for _, match := range matches {
		if len(match) > 1 {
			// 添加换行符分隔不同的script标签内容
			jsCode.WriteString(match[1])
			jsCode.WriteString("\n")
		}
	}

	return jsCode.String()
}

// === JavaScript API提取方法 ===

// ExtractJSAPIs 从JavaScript代码中提取API路径
// 优化版本：移除过度API验证，提取所有路径
func (eu *ExtractorUtils) ExtractJSAPIs(jsCode string) []string {
	var results []string
	seen := make(map[string]bool)

	// 获取JavaScript专用路径提取模式
	patterns := eu.regexManager.GetJSPathPatterns()

	// 处理所有路径提取模式
	for patternName, pattern := range patterns {
		if pattern == nil {
			continue
		}

		matches := pattern.FindAllStringSubmatch(jsCode, -1)

		for _, match := range matches {
			if len(match) > 1 {
				var extractedPath string
				if len(match) > 2 && match[2] != "" {
					// 处理组合匹配（如变量+路径）
					extractedPath = match[2]
				} else {
					extractedPath = match[1]
				}

				// 清理路径
				cleanedPath := eu.cleanAPIPath(extractedPath)
				if cleanedPath == "" {
					continue
				}

				// 避免重复
				if seen[cleanedPath] {
					continue
				}

				// 使用JS专用路径验证进行合理降噪
				if eu.filterUtils.IsValidJSPath(cleanedPath) {
					results = append(results, cleanedPath)
					seen[cleanedPath] = true
				}
			}
		}

		// 记录提取来源（用于调试）
		_ = patternName
	}

	return results
}

// cleanAPIPath 清理API路径
// 优化版本：只进行基本清理，不限制路径格式
func (eu *ExtractorUtils) cleanAPIPath(path string) string {
	if path == "" {
		return ""
	}

	// 移除引号和空白字符
	path = strings.Trim(path, "'\"` \t\n\r")

	// 移除末尾的特殊字符（但保留路径结构字符）
	path = strings.TrimRight(path, ",;)}")

	// 处理转义字符
	path = strings.ReplaceAll(path, "\\/", "/")

	// 保留查询参数和片段标识符（它们也是有效的路径信息）
	// 移除之前的查询参数和片段标识符过滤逻辑

	// 移除重复的斜杠（但保留协议中的//）
	for strings.Contains(path, "//") && !strings.Contains(path, "://") {
		path = strings.ReplaceAll(path, "//", "/")
	}

	return path
}

// === 密钥提取方法 ===

// ExtractSecrets 提取密钥信息
func (eu *ExtractorUtils) ExtractSecrets(content string, source string) []InfoItem {
	var results []InfoItem

	// 获取所有密钥相关的正则表达式
	patterns := eu.regexManager.GetSecretPatterns()

	for secretType, pattern := range patterns {
		matches := pattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				value := eu.stringUtils.CleanSensitiveValue(match[1])
				if !eu.filterUtils.IsObviousNonSecret(value) && eu.filterUtils.IsValidSecret(value, secretType) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}
	}

	return eu.uniqueInfoItems(results)
}

// === 工具方法 ===

// uniqueInfoItems 对InfoItem数组进行去重
func (eu *ExtractorUtils) uniqueInfoItems(items []InfoItem) []InfoItem {
	seen := make(map[string]bool)
	var result []InfoItem

	for _, item := range items {
		key := item.Value + "|" + item.Source
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}

	return result
}

// ExtractFromContext 基于上下文的智能提取
func (eu *ExtractorUtils) ExtractFromContext(content string, source string) ([]InfoItem, []InfoItem) {
	var urls []InfoItem
	var paths []InfoItem

	// 对于路径提取，我们需要保留字符串字面量，因为路径通常在字符串中
	// 只移除注释，保留字符串内容
	cleanContent := eu.stringUtils.RemoveComments(content)

	// 在清理后的内容中提取URL（所有文件类型）
	// 对于URL提取，可以进一步清理字符串字面量
	urlCleanContent := eu.stringUtils.RemoveStrings(cleanContent)
	urls = append(urls, eu.ExtractURLs(urlCleanContent, source)...)

	// JavaScript文件和HTML文件都提取path，使用保留字符串的内容
	isJSFile := eu.stringUtils.IsJavaScriptFile(source)
	isHTMLFile := strings.Contains(strings.ToLower(source), ".html") || strings.Contains(content, "<script")

	if isJSFile || isHTMLFile {
		paths = append(paths, eu.ExtractPaths(cleanContent, source)...)
	}

	return urls, paths
}

// ProcessTemplateString 处理模板字符串，移除变量占位符
func (eu *ExtractorUtils) ProcessTemplateString(template string) string {
	// 移除反引号
	template = strings.Trim(template, "`")

	// 简化变量替换：将${variable}替换为占位符
	variablePattern := regexp.MustCompile(`\$\{[^}]+\}`)
	processed := variablePattern.ReplaceAllString(template, "{id}")

	return processed
}

// uniqueInfoItems 对InfoItem数组进行去重 - 公开方法
func (eu *ExtractorUtils) UniqueInfoItems(items []InfoItem) []InfoItem {
	return eu.uniqueInfoItems(items)
}
