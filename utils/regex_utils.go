package utils

import (
	"regexp"
	"sync"
)

// RegexManager 正则表达式管理器 - 统一管理所有正则表达式
// 功能：预编译、缓存、分类管理项目中的所有正则表达式
type RegexManager struct {
	patterns map[string]*regexp.Regexp // 预编译的正则表达式缓存
	mutex    sync.RWMutex              // 读写锁，保证并发安全
}

// 全局正则表达式管理器实例（单例模式）
var (
	globalRegexManager *RegexManager
	regexOnce          sync.Once
)

// GetRegexManager 获取全局正则表达式管理器实例
func GetRegexManager() *RegexManager {
	regexOnce.Do(func() {
		globalRegexManager = NewRegexManager()
	})
	return globalRegexManager
}

// NewRegexManager 创建新的正则表达式管理器
func NewRegexManager() *RegexManager {
	rm := &RegexManager{
		patterns: make(map[string]*regexp.Regexp),
	}

	// 预编译所有常用正则表达式
	rm.initializePatterns()

	return rm
}

// initializePatterns 初始化所有正则表达式模式
func (rm *RegexManager) initializePatterns() {
	// === 敏感信息提取正则表达式 ===

	// 身份证号码（18位和15位）
	rm.compileAndStore("sfz", `(?:^|[^0-9])([1-9]\d{5}(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[0-9Xx])(?:[^0-9]|$)`)

	// 手机号码（包括400客服热线和固定电话）
	rm.compileAndStore("phone_mobile", `(?:^|[^0-9])(1[3-9]\d{9})(?:[^0-9]|$)`)
	rm.compileAndStore("phone_400", `(?:^|[^0-9])(400[-\s]?\d{3}[-\s]?\d{4})(?:[^0-9]|$)`)
	rm.compileAndStore("phone_fixed", `(?:^|[^0-9])(0\d{2,3}[-\s]?\d{7,8})(?:[^0-9]|$)`)

	// 邮箱地址
	rm.compileAndStore("email", `([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})`)

	// IP地址（IPv4）
	rm.compileAndStore("ipv4", `(?:^|[^0-9.])((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(?:[^0-9.]|$)`)

	// 域名
	rm.compileAndStore("domain", `(?:^|[^a-zA-Z0-9.-])([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:[^a-zA-Z0-9.-]|$)`)

	// === URL和路径提取正则表达式 ===

	// 完整URL（HTTP/HTTPS）
	rm.compileAndStore("url_http", `(?:^|[^a-zA-Z0-9])(https?://[^\s'"<>(){}[\]]+)`)

	// 相对路径（以/开头）- 扩展版本，支持更多字符
	rm.compileAndStore("path_absolute", `(?:^|[^a-zA-Z0-9])(/[a-zA-Z0-9._\-\?\&\=\{\}\$\[\]%:@!~,;]+)`)

	// API路径模式 - 保持原有逻辑
	rm.compileAndStore("api_path", `(?:^|[^a-zA-Z0-9])(/(?:api|v\d+)/[a-zA-Z0-9._/-]+)`)

	// JavaScript专用路径提取模式 - 按照用户提供的思路
	// 完整路径：以/、../或./开头的路径字符串
	rm.compileAndStore("js_path_complete", `['"]((\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\]*[^\>\< \)\(\{\}\,\'\"\\]*)['"]`)
	// 不完整路径：不以/、../或./开头但包含/的字符串
	rm.compileAndStore("js_path_incomplete", `['"]([^\/\.\>\< \)\(\{\}\,\'\"\\][^\/\>\< \)\(\{\}\,\'\"\\]*\/[^\/\>\< \)\(\{\}\,\'\"\\]*)['"]`)
	// 模板字符串中的路径（保留原有逻辑）
	rm.compileAndStore("js_path_template", "`([^`]*[/\\][^`]*?)`")

	// === JavaScript API提取正则表达式 ===

	// fetch() 调用 - 基础模式
	rm.compileAndStore("js_fetch", `fetch\s*\(\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)
	// fetch() 调用 - 变量拼接模式
	rm.compileAndStore("js_fetch_concat", `fetch\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\+\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)

	// XMLHttpRequest
	rm.compileAndStore("js_xhr", `\.open\s*\(\s*['"`+"`"+`][^'"`+"`"+`]*['"`+"`"+`]\s*,\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)

	// axios 请求 - 方法调用
	rm.compileAndStore("js_axios", `axios\s*\.\s*(?:get|post|put|delete|patch)\s*\(\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)
	// axios 请求 - 配置对象
	rm.compileAndStore("js_axios_config", `axios\s*\(\s*\{\s*[^}]*url\s*:\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)

	// jQuery AJAX - 方法调用
	rm.compileAndStore("js_jquery", `\$\.(?:ajax|get|post)\s*\(\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)
	// jQuery AJAX - 配置对象
	rm.compileAndStore("js_jquery_config", `\$\.ajax\s*\(\s*\{\s*[^}]*url\s*:\s*['"`+"`"+`]([^'"`+"`"+`]+)['"`+"`"+`]`)

	// 模板字符串路径
	rm.compileAndStore("js_template_path", "`([^`]*\\/[^`]*)`")

	// 对象属性中的路径
	rm.compileAndStore("js_object_path", `(?:url|endpoint|path|route|api)\s*:\s*['"](\/[a-zA-Z0-9\/_\-\.\?\&\=\{\}\$\[\]]+)['"]`)

	// 数组中的路径
	rm.compileAndStore("js_array_path", `\[\s*['"](\/[a-zA-Z0-9\/_\-\.\?\&\=\{\}\$\[\]]+)['"]`)

	// 完整HTTP链接
	rm.compileAndStore("js_http_url", `['"`+"`"+`](https?:\/\/[^\/]+\/[a-zA-Z0-9_\-\/.?&=]+)['"`+"`"+`]`)

	// === 密钥提取正则表达式 ===

	// AWS访问密钥
	rm.compileAndStore("secret_aws_access", `(?i)(?:aws_access_key_id|access_key)\s*[:=]\s*['"]?(AKIA[0-9A-Z]{16})['"]?`)

	// AWS秘密密钥
	rm.compileAndStore("secret_aws_secret", `(?i)(?:aws_secret_access_key|secret_key)\s*[:=]\s*['"]?([A-Za-z0-9/+=]{40})['"]?`)

	// GitHub Token
	rm.compileAndStore("secret_github", `(?i)(?:github_token|gh[ps]_[a-zA-Z0-9]{36})`)

	// JWT Token
	rm.compileAndStore("secret_jwt", `(?i)(?:jwt|token)\s*[:=]\s*['"]?(eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+)['"]?`)

	// API密钥通用模式
	rm.compileAndStore("secret_api_key", `(?i)(?:api_key|apikey|key)\s*[:=]\s*['"]?([A-Za-z0-9]{20,})['"]?`)

	// === 过滤和验证正则表达式 ===

	// 私有IP地址
	rm.compileAndStore("private_ip", `^(?:10\.|172\.(?:1[6-9]|2[0-9]|3[01])\.|192\.168\.|127\.|169\.254\.|224\.|240\.)`)

	// 文件扩展名
	rm.compileAndStore("file_extension", `\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$`)

	// JavaScript对象属性访问
	rm.compileAndStore("js_property", `[a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*`)

	// 测试数据模式
	rm.compileAndStore("test_data", `(?i)(?:test|demo|example|sample|mock|fake)`)
}

// compileAndStore 编译并存储正则表达式
func (rm *RegexManager) compileAndStore(name, pattern string) {
	if compiled, err := regexp.Compile(pattern); err == nil {
		rm.patterns[name] = compiled
	}
}

// GetPattern 获取预编译的正则表达式
func (rm *RegexManager) GetPattern(name string) *regexp.Regexp {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	return rm.patterns[name]
}

// GetPatterns 获取多个正则表达式
func (rm *RegexManager) GetPatterns(names ...string) map[string]*regexp.Regexp {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	result := make(map[string]*regexp.Regexp)
	for _, name := range names {
		if pattern, exists := rm.patterns[name]; exists {
			result[name] = pattern
		}
	}

	return result
}

// AddPattern 动态添加新的正则表达式
func (rm *RegexManager) AddPattern(name, pattern string) error {
	compiled, err := regexp.Compile(pattern)
	if err != nil {
		return err
	}

	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	rm.patterns[name] = compiled
	return nil
}

// GetAllPatternNames 获取所有已注册的正则表达式名称
func (rm *RegexManager) GetAllPatternNames() []string {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	names := make([]string, 0, len(rm.patterns))
	for name := range rm.patterns {
		names = append(names, name)
	}

	return names
}

// === 便捷方法：按类型获取正则表达式组 ===

// GetSensitiveInfoPatterns 获取敏感信息提取相关的正则表达式
func (rm *RegexManager) GetSensitiveInfoPatterns() map[string]*regexp.Regexp {
	return rm.GetPatterns("sfz", "phone_mobile", "phone_400", "phone_fixed", "email", "ipv4", "domain")
}

// GetURLPathPatterns 获取URL和路径提取相关的正则表达式
func (rm *RegexManager) GetURLPathPatterns() map[string]*regexp.Regexp {
	return rm.GetPatterns("url_http", "path_absolute", "api_path")
}

// GetJSPathPatterns 获取JavaScript路径提取相关的正则表达式
func (rm *RegexManager) GetJSPathPatterns() map[string]*regexp.Regexp {
	return rm.GetPatterns("js_path_complete", "js_path_incomplete", "js_path_template")
}

// GetSecretPatterns 获取密钥提取相关的正则表达式
func (rm *RegexManager) GetSecretPatterns() map[string]*regexp.Regexp {
	return rm.GetPatterns("secret_aws_access", "secret_aws_secret", "secret_github", "secret_jwt", "secret_api_key")
}

// GetJSAPIPatterns 获取JavaScript API提取相关的正则表达式
func (rm *RegexManager) GetJSAPIPatterns() []*regexp.Regexp {
	patternNames := []string{
		"js_fetch", "js_fetch_concat", "js_xhr", "js_axios", "js_axios_config",
		"js_jquery", "js_jquery_config", "js_template_path", "js_object_path",
		"js_array_path", "js_http_url",
	}

	var patterns []*regexp.Regexp
	for _, name := range patternNames {
		if pattern := rm.GetPattern(name); pattern != nil {
			patterns = append(patterns, pattern)
		}
	}

	return patterns
}

// GetFilterPatterns 获取过滤和验证相关的正则表达式
func (rm *RegexManager) GetFilterPatterns() map[string]*regexp.Regexp {
	return rm.GetPatterns("private_ip", "file_extension", "js_property", "test_data")
}
