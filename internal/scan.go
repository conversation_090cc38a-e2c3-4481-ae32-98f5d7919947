package internal

import (
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"web_scanner/config"
	"web_scanner/core"

	"github.com/playwright-community/playwright-go"
)

// 全局敏感信息提取器实例和初始化控制
// globalSensitiveInfoExtractor 全局敏感信息提取器实例，使用单例模式确保资源复用
// extractorOnce 确保敏感信息提取器只初始化一次的同步控制器
var (
	globalSensitiveInfoExtractor *FindSomethingExtractor
	extractorOnce                sync.Once
)

// GetSensitiveInfoExtractor 获取全局敏感信息提取器实例
// 使用单例模式确保整个应用程序中只有一个提取器实例，提高性能和资源利用率
//
// 返回值：
//   - *FindSomethingExtractor: findsomething敏感信息提取器实例
func GetSensitiveInfoExtractor() *FindSomethingExtractor {
	extractorOnce.Do(func() {
		globalSensitiveInfoExtractor = NewFindSomethingExtractor()
	})
	return globalSensitiveInfoExtractor
}

// ScanSession 扫描会话结构体
// 封装了单次扫描任务所需的所有浏览器相关资源和上下文信息
type ScanSession struct {
	pw      *playwright.Playwright      // Playwright实例，用于控制浏览器
	browser playwright.Browser          // 浏览器实例，提供页面创建和管理功能
	context playwright.BrowserContext   // 浏览器上下文，隔离不同扫描任务的状态
	pool    *BrowserPool                // 浏览器池引用，用于会话归还和资源管理
	tracker *URLProcessingTracker       // URL处理跟踪器（可选），用于监控请求活跃度
}

// InitScanSession 创建独立的扫描会话（不使用浏览器池）
//
// 已弃用：此方法为每次扫描创建新的浏览器实例，资源消耗较大
// 建议使用 BrowserPool.Get() 获取会话以提高性能和资源利用率
//
// 返回值：
//   - *ScanSession: 新创建的扫描会话实例
//   - error: 创建过程中的错误信息
func InitScanSession() (*ScanSession, error) {
	pw, err := playwright.Run()
	if err != nil {
		return nil, fmt.Errorf("Playwright 启动失败: %w", err)
	}

	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(true),
		Args: []string{
			"--disable-setuid-sandbox",
			"--disable-blink-features=AutomationControlled",
			"--disable-web-security",
			"--no-sandbox",
			"--disable-gpu",
			"--disable-dev-shm-usage",
			"--disable-accelerated-2d-canvas",
			"--no-first-run",
			"--no-zygote",
			"--single-process",
			"--disable-extensions",
		},
	})
	if err != nil {
		pw.Stop()
		return nil, fmt.Errorf("浏览器启动失败: %w", err)
	}

	context, err := browser.NewContext(playwright.BrowserNewContextOptions{
		UserAgent: playwright.String("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"),
		Viewport: &playwright.Size{
			Width:  1920,
			Height: 1080,
		},
		JavaScriptEnabled: playwright.Bool(true),
		HasTouch:          playwright.Bool(false),
		IsMobile:          playwright.Bool(false),
		Locale:            playwright.String("zh-CN"),
		TimezoneId:        playwright.String("Asia/Shanghai"),
		IgnoreHttpsErrors: playwright.Bool(true),
	})
	if err != nil {
		browser.Close()
		pw.Stop()
		return nil, err
	}

	return &ScanSession{
		pw:      pw,
		browser: browser,
		context: context,
		pool:    nil, // 不来自池
	}, nil
}

// GetContext 获取浏览器上下文
func (s *ScanSession) GetContext() playwright.BrowserContext {
	return s.context
}

func (s *ScanSession) Close() {
	if s.pool != nil {
		// 如果来自池，则归还到池中
		s.pool.Put(s)
	} else {
		// 否则直接关闭
		s.CloseContext()
		if s.browser != nil {
			s.browser.Close()
		}
		if s.pw != nil {
			s.pw.Stop()
		}
	}
}

// CloseContext 只关闭上下文，不关闭浏览器
func (s *ScanSession) CloseContext() {
	if s.context != nil {
		s.context.Close()
		s.context = nil
	}
}

// SetTracker 设置URL处理跟踪器
func (s *ScanSession) SetTracker(tracker *URLProcessingTracker) {
	s.tracker = tracker
}

// RunScan 执行扫描任务 - 核心扫描引擎
// 参数说明：
//   - targetURL: 目标URL，必须是完整的HTTP/HTTPS地址
//   - maxDepth: 最大扫描深度，0表示只扫描当前页面，>0表示递归扫描子链接
//
// 返回值：
//   - []ScanResult: 扫描结果列表，包含所有发现的页面信息
//   - error: 扫描过程中的错误信息
func (s *ScanSession) RunScan(targetURL string, maxDepth int) ([]ScanResult, error) {
	// 添加互斥锁保护共享资源，确保并发安全
	var visitedMu sync.Mutex
	var resultsMu sync.Mutex
	visited := make(map[string]bool) // 记录已访问的URL，避免重复扫描
	var results []ScanResult         // 存储所有扫描结果

	// 使用更简单可靠的递归扫描方式，避免并发问题
	var scanRecursive func(url string, parent string, currentDepth int) error
	scanRecursive = func(url string, parent string, currentDepth int) error {
		// 检查URL是否已访问，使用互斥锁保护
		visitedMu.Lock()
		if visited[url] || currentDepth > maxDepth {
			visitedMu.Unlock()
			return nil
		}
		visited[url] = true
		visitedMu.Unlock()

		// 只在深度0时输出扫描信息，减少日志噪音
		if currentDepth == 0 {
			fmt.Printf("🔍 扫描 %s\n", url)
		}
		result, links, err := s.scanSingle(url)
		if err != nil {
			fmt.Printf("❌ 扫描失败 %s: %v\n", url, err)
			return nil
		}
		result.ParentURL = parent

		// 使用互斥锁保护结果写入
		resultsMu.Lock()
		results = append(results, result)
		resultsMu.Unlock()

		// 如果还没达到最大深度，继续扫描发现的链接
		if currentDepth < maxDepth {
			// 使用配置文件中的子链接并发数设置
			linkConcurrent := config.CurrentScanConfig.GetLinkConcurrency()
			semaphore := make(chan struct{}, linkConcurrent)
			var linkWg sync.WaitGroup

			// 获取请求延迟配置
			requestDelay := config.CurrentScanConfig.GetRequestDelay()

			for _, link := range links {
				if sameDomain(link, targetURL) {
					linkWg.Add(1)

					// 如果有跟踪器，增加子链接扫描计数
					if s.tracker != nil {
						s.tracker.AddActiveSubScan()
					}

					go func(linkURL string) {
						defer linkWg.Done()
						defer func() {
							// 完成子链接扫描时减少计数
							if s.tracker != nil {
								s.tracker.CompleteSubScan()
							}
						}()

						semaphore <- struct{}{}
						defer func() { <-semaphore }()

						// 添加请求延迟，避免过于频繁的请求
						if requestDelay > 0 {
							time.Sleep(requestDelay)
						}

						_ = scanRecursive(linkURL, url, currentDepth+1)
					}(link)
				}
			}
			linkWg.Wait()
		}
		return nil
	}

	// 开始递归扫描
	_ = scanRecursive(targetURL, "", 0)
	return results, nil
}

func sameDomain(link1, link2 string) bool {
	u1, err1 := url.Parse(link1)
	u2, err2 := url.Parse(link2)
	if err1 != nil || err2 != nil {
		return false
	}
	return u1.Host == u2.Host
}

// scanSingle 扫描单个页面 - 核心页面分析功能
// 参数说明：
//   - targetURL: 要扫描的目标URL
//
// 返回值：
//   - ScanResult: 页面扫描结果，包含资源列表、API信息、敏感信息等
//   - []string: 发现的链接列表，用于递归扫描
//   - error: 扫描过程中的错误
func (s *ScanSession) scanSingle(targetURL string) (ScanResult, []string, error) {
	// 创建新的页面实例
	page, err := s.context.NewPage()
	if err != nil {
		// 使用统一的错误管理器
		errorManager := core.GetErrorManager()
		scanErr := errorManager.NewError(core.ErrCodePageLoadFailed, "创建页面失败", targetURL, "scan", err)
		return ScanResult{}, nil, scanErr
	}
	defer page.Close()

	// 初始化各类资源列表
	var htmlList, jsList, cssList, imgList, fontList, docList, otherList []string
	var apiList []APIEntry    // API接口列表
	var aLinks []string       // 页面中的链接
	seen := map[string]bool{} // 去重映射表

	// 使用配置文件中的内存设置，而不是硬编码
	maxCache := config.CurrentScanConfig.Memory.MaxResponseCache
	maxMemoryMB := int64(config.CurrentScanConfig.Memory.MemoryThresholdMB / 20) // 使用总内存的1/20作为响应缓存
	if maxMemoryMB < 10 {
		maxMemoryMB = 10 // 最小10MB
	}
	responseManager := NewResponseManagerWithMemoryLimit(maxCache, maxMemoryMB)
	defer responseManager.Close()

	// 获取全局敏感信息提取器
	extractor := GetSensitiveInfoExtractor()

	result := ScanResult{
		URL:       targetURL,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	addURL := func(t string, url string) {
		// 忽略data:image开头的base64编码图片链接
		if strings.HasPrefix(url, "data:image") {
			return
		}
		if seen[url] {
			return
		}
		seen[url] = true
		switch t {
		case "html":
			htmlList = append(htmlList, url)
		case "js":
			jsList = append(jsList, url)
		case "css":
			cssList = append(cssList, url)
		case "image":
			imgList = append(imgList, url)
		case "font":
			fontList = append(fontList, url)
		case "document":
			docList = append(docList, url)
		case "other":
			otherList = append(otherList, url)
		}
	}

	// 监听所有请求事件，必须在页面跳转前设置，确保捕获所有API请求
	page.On("request", func(req playwright.Request) {
		url := req.URL()
		method := req.Method()
		// 检测资源类型
		typ := detectResourceType(url, req.ResourceType(), method)

		// 更新URL处理跟踪器（如果存在）
		if s.tracker != nil {
			s.tracker.AddActiveRequest()
		}

		// 判断是否为API请求（如XHR、fetch、websocket等）
		if ClassifyAPI(req) {
			entry := APIEntry{
				URL:        url,
				Method:     method,
				PostData:   "",
				FromScript: true,
			}
			if method == "POST" {
				if body, err := req.PostData(); err == nil {
					entry.PostData = body
				}
			}
			apiList = append(apiList, entry)
		} else {
			addURL(typ, url)
		}
	})

	page.On("response", func(res playwright.Response) {
		url := res.URL()
		responseManager.Store(url, res)

		// 更新URL处理跟踪器（如果存在）
		if s.tracker != nil {
			s.tracker.CompleteRequest()
		}
	})

	// 设置页面超时时间（使用配置文件中的设置）
	pageTimeout := config.CurrentScanConfig.GetPageLoadTimeout()
	page.SetDefaultTimeout(float64(pageTimeout.Milliseconds()))

	resp, err := page.Goto(targetURL)
	if err != nil {
		// 使用统一的错误管理器
		errorManager := core.GetErrorManager()
		scanErr := errorManager.NewError(core.ErrCodePageLoadFailed, "页面加载失败", targetURL, "scan", err)
		return ScanResult{}, nil, scanErr
	}

	// 等待页面加载完成（使用配置文件中的超时设置）
	page.WaitForLoadState()
	// 使用配置文件中的页面等待时间设置
	pageWaitTime := config.CurrentScanConfig.GetPageWaitTime()
	page.WaitForTimeout(float64(pageWaitTime.Milliseconds()))

	// 获取页面标题、状态码和HTML内容
	title, _ := page.Title()
	result.Title = title
	result.Status = resp.Status()

	// 获取页面HTML内容用于敏感信息提取
	htmlContent, _ := page.Content()

	if domUrls, err := ExtractDOMResources(page); err == nil {
		for _, u := range domUrls {
			typ := detectResourceType(u, "", "")
			addURL(typ, u)
		}
	}

	if hrefs, err := page.Evaluate(`() => {
		return [...document.querySelectorAll("a[href]")]
			.map(a => a.href).filter(h => h.startsWith("http"))
	}`); err == nil {
		if arr, ok := hrefs.([]interface{}); ok {
			for _, raw := range arr {
				if str, ok := raw.(string); ok {
					aLinks = append(aLinks, str)
				}
			}
		}
	}

	for _, frame := range page.Frames() {
		if frame == nil || frame.URL() == targetURL {
			continue
		}
		if urls, err := ExtractDOMResources(frame); err == nil {
			for _, u := range urls {
				typ := detectResourceType(u, "", "")
				addURL(typ, u)
			}
		}
	}

	for i, api := range apiList {
		if res, ok := responseManager.Get(api.URL); ok {
			apiList[i].Status = res.Status()
			apiList[i].Mime = res.Headers()["content-type"]
			if strings.Contains(apiList[i].Mime, "application/json") {
				if body, err := res.Text(); err == nil {
					apiList[i].Response = body
				}
			}
		}
	}

	// === 从HTML内容中提取敏感信息（使用findsomething提取器）===
	if htmlContent != "" {
		htmlSensitiveInfo := extractor.ExtractSensitiveInfo(htmlContent, targetURL)
		if htmlSensitiveInfo != nil {
			// 使用统一的合并逻辑
			result.SensitiveInfo = MergeSensitiveInfo(result.SensitiveInfo, htmlSensitiveInfo)
		}
	}

	// === 从所有已获取的JS文件中提取敏感信息（包括路径） ===
	// 注意：不再从JS中提取API链接到APIList，路径信息直接保存到js_sensitive_info表的path字段
	for _, jsURL := range jsList {
		// 检查响应管理器中是否有该JS文件的响应内容
		if res, ok := responseManager.Get(jsURL); ok {
			// 只处理状态码为200的JS文件
			if res.Status() == 200 {
				jsCode, err := res.Text()
				if err == nil {
					// 使用findsomething敏感信息提取器，提高性能和准确性
					// 这里会提取路径信息并保存到SensitiveInfo.Path字段中
					sensitiveInfo := extractor.ExtractSensitiveInfo(jsCode, jsURL)
					if sensitiveInfo != nil {
						// 使用统一的合并逻辑
						result.SensitiveInfo = MergeSensitiveInfo(result.SensitiveInfo, sensitiveInfo)
					}
				}
			}
		}
	}

	// 更新结果
	result.HTMLList = htmlList
	result.JSList = jsList
	result.CSSList = cssList
	result.ImageList = imgList
	result.FontList = fontList
	result.DocList = docList
	result.APIList = apiList
	result.OtherList = otherList

	// 更新计数
	result.Counts.HTML = len(htmlList)
	result.Counts.JS = len(jsList)
	result.Counts.CSS = len(cssList)
	result.Counts.Image = len(imgList)
	result.Counts.Font = len(fontList)
	result.Counts.Document = len(docList)
	result.Counts.API = len(apiList)
	result.Counts.Other = len(otherList)

	return result, aLinks, nil
}

func detectResourceType(url, resourceType, method string) string {
	u := strings.ToLower(url)

	// HTML文档类型
	if resourceType == "document" {
		return "html"
	}

	// JavaScript脚本类型（包括带参数的JS文件）
	if resourceType == "script" || strings.HasSuffix(u, ".js") ||
		strings.Contains(u, ".js?") || strings.Contains(u, ".js&") {
		return "js"
	}

	// CSS样式文件类型（包括带参数的CSS文件）
	if strings.HasSuffix(u, ".css") || strings.Contains(u, ".css?") ||
		strings.Contains(u, ".css&") || strings.Contains(u, "/css/") {
		return "css"
	}

	// 字体文件类型
	if strings.HasSuffix(u, ".woff") || strings.HasSuffix(u, ".woff2") ||
		strings.HasSuffix(u, ".ttf") || strings.HasSuffix(u, ".otf") ||
		strings.HasSuffix(u, ".eot") || strings.Contains(u, "/fonts/") {
		return "font"
	}

	// 文档文件类型
	if strings.HasSuffix(u, ".pdf") || strings.HasSuffix(u, ".doc") ||
		strings.HasSuffix(u, ".docx") || strings.HasSuffix(u, ".xlsx") ||
		strings.HasSuffix(u, ".xls") || strings.HasSuffix(u, ".txt") ||
		strings.HasSuffix(u, ".ppt") || strings.HasSuffix(u, ".pptx") {
		return "document"
	}

	// 图片文件类型（包括带参数的图片文件）
	if strings.HasSuffix(u, ".png") || strings.HasSuffix(u, ".jpg") ||
		strings.HasSuffix(u, ".jpeg") || strings.HasSuffix(u, ".gif") ||
		strings.HasSuffix(u, ".svg") || strings.HasSuffix(u, ".webp") ||
		strings.HasSuffix(u, ".ico") || strings.Contains(u, ".png?") ||
		strings.Contains(u, ".jpg?") || strings.Contains(u, ".jpeg?") ||
		strings.Contains(u, ".gif?") || strings.Contains(u, "/img/") ||
		strings.Contains(u, "/images/") {
		return "image"
	}

	// API请求类型（这里不应该被调用，因为API通过ClassifyAPI函数单独处理）
	if resourceType == "xhr" || resourceType == "fetch" || method == "POST" {
		return "api"
	}

	// 其他未分类的资源
	return "other"
}

// 注意：ExtractAPIsFromJS函数已被移除
// 路径提取现在完全由findsomething提取器处理，路径信息直接保存到js_sensitive_info表的path字段中
// 不再将从JavaScript静态提取的路径拼接为完整URL保存到resource_links表中

// 注意：cleanAPIPath函数已被移除
// 路径清理现在由findsomething提取器的内部逻辑处理

// 注意：isValidAPIPath函数已被移除
// API路径验证现在由ClassifyAPI函数处理，只识别真正的动态请求

// 注意：isValidAPIPathRelaxed函数已被移除
// API路径验证现在由ClassifyAPI函数处理，只识别真正的动态请求

// 注意：isValidAPIPathStrict和isValidAPIURL函数已被移除
// API路径验证现在由ClassifyAPI函数处理，只识别真正的动态请求

// 新增函数：将相对路径转换为绝对路径
func resolveRelativeURL(baseURL, relativeURL string) string {
	if strings.HasPrefix(relativeURL, "http") {
		return relativeURL
	}
	base, err := url.Parse(baseURL)
	if err != nil {
		return relativeURL
	}
	rel, err := url.Parse(relativeURL)
	if err != nil {
		return relativeURL
	}
	abs := base.ResolveReference(rel)
	return abs.String()
}
