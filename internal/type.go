package internal

// InfoItem 敏感信息项
type InfoItem struct {
	Value  string `json:"value"`
	Source string `json:"source"`
}

// SensitiveInfo 敏感信息结构
type SensitiveInfo struct {
	SFZ       []InfoItem `json:"sfz,omitempty"`       // 身份证
	Phone     []InfoItem `json:"phone,omitempty"`     // 电话号码（手机号、400客服热线、固定电话）
	Mail      []InfoItem `json:"mail,omitempty"`      // 邮箱
	IP        []InfoItem `json:"ip,omitempty"`        // IP地址
	IPPort    []InfoItem `json:"ipport,omitempty"`    // IP:端口
	Domain    []InfoItem `json:"domain,omitempty"`    // 域名
	URL       []InfoItem `json:"url,omitempty"`       // URL
	Secret    []InfoItem `json:"secret,omitempty"`    // 密钥
	Path      []InfoItem `json:"path,omitempty"`      // 路径（统一归类，不区分完整和不完整）
	JWT       []InfoItem `json:"jwt,omitempty"`       // JWT令牌
	Algorithm []InfoItem `json:"algorithm,omitempty"` // 算法
}

type APIEntry struct {
	URL        string `json:"url"`
	Method     string `json:"method"`
	PostData   string `json:"postData"`
	Status     int    `json:"status"`
	Mime       string `json:"mime"`
	Response   string `json:"response,omitempty"` // debug 模式下记录
	FromScript bool   `json:"fromScript"`         // 是否动态请求
}

type Resource struct {
	URL     string `json:"url"`
	Type    string `json:"type"`
	FromDOM bool   `json:"fromDom"`
	FromJS  bool   `json:"fromJs"`
	Status  int    `json:"status"`
	Mime    string `json:"mime"`
}

type ScanResult struct {
	URL       string     `json:"url"`
	ParentURL string     `json:"parent,omitempty"`
	Timestamp string     `json:"timestamp"`
	Title     string     `json:"title"`
	Status    int        `json:"status"`
	HTMLList  []string   `json:"htmlList"`
	JSList    []string   `json:"jsList"`
	CSSList   []string   `json:"cssList"`
	ImageList []string   `json:"imageList"`
	FontList  []string   `json:"fontList"`     // 新增：字体文件列表
	DocList   []string   `json:"docList"`      // 新增：文档文件列表
	APIList   []APIEntry `json:"apiList"`
	OtherList []string   `json:"otherList"`

	// 敏感信息
	SensitiveInfo *SensitiveInfo `json:"sensitiveInfo,omitempty"`

	Counts struct {
		HTML     int `json:"html"`
		JS       int `json:"js"`
		CSS      int `json:"css"`
		Image    int `json:"image"`
		Font     int `json:"font"`     // 新增：字体文件计数
		Document int `json:"document"` // 新增：文档文件计数
		API      int `json:"api"`
		Other    int `json:"other"`
	} `json:"counts"`
}
