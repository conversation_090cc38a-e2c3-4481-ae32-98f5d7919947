package internal

import (
	"encoding/json"
	"io/ioutil"
	"regexp"
	"strings"
	"web_scanner/core"
)

// FindSomethingExtractor findsomething项目的敏感信息提取器
// 完全基于findsomething项目的提取逻辑实现，保持高准确性和覆盖率
type FindSomethingExtractor struct {
	logger       *core.Logger
	errorManager *core.ErrorManager
	
	// 预编译的正则表达式，提高性能
	sfzRegex           *regexp.Regexp
	mobileRegex        *regexp.Regexp
	mailRegex          *regexp.Regexp
	ipRegex            *regexp.Regexp
	ipPortRegex        *regexp.Regexp
	domainRegex        *regexp.Regexp
	pathRegex          *regexp.Regexp
	incompletePathRegex *regexp.Regexp
	urlRegex           *regexp.Regexp
	jwtRegex           *regexp.Regexp
	algorithmRegex     *regexp.Regexp
	
	// nuclei密钥检测正则表达式数组
	nucleiRegexes []*regexp.Regexp
}

// NewFindSomethingExtractor 创建findsomething敏感信息提取器
func NewFindSomethingExtractor() *FindSomethingExtractor {
	extractor := &FindSomethingExtractor{
		logger:       core.GetLogger(),
		errorManager: core.GetErrorManager(),
	}
	
	// 初始化所有正则表达式
	extractor.initializeRegexes()
	
	return extractor
}

// initializeRegexes 初始化所有正则表达式
// 基于findsomething项目的extract_info函数中的正则表达式
func (fse *FindSomethingExtractor) initializeRegexes() {
	var err error
	
	// 身份证号码正则（支持15位和18位）
	fse.sfzRegex, err = regexp.Compile(`['"]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3})|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "身份证正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// 手机号码正则
	fse.mobileRegex, err = regexp.Compile(`['"](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "手机号正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// 邮箱地址正则
	fse.mailRegex, err = regexp.Compile(`['"]([a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.[a-zA-Z]{2,})['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "邮箱正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// IP地址正则
	fse.ipRegex, err = regexp.Compile(`['"]([a-zA-Z0-9]+://)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(/.*)?['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "IP地址正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// IP:端口正则
	fse.ipPortRegex, err = regexp.Compile(`['"]([a-zA-Z0-9]+://)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5})(/.*)?['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "IP端口正则编译失败", map[string]interface{}{"error": err.Error()})
	}
	
	// 域名正则（支持多种顶级域名）
	domainPattern := `['"]([a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw))['"]`
	fse.domainRegex, err = regexp.Compile(domainPattern)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "域名正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// 完整路径正则（以/、../或./开头）
	fse.pathRegex, err = regexp.Compile(`['"]((\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\][^\>\< \)\(\{\}\,\'\"\\]*)['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "路径正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// 不完整路径正则（不以/开头但包含/）
	fse.incompletePathRegex, err = regexp.Compile(`['"]([^\/\>\< \)\(\{\}\,\'\"\\][\w\/]*\/[\w\/]*)['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "不完整路径正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// URL正则（完整URL）
	urlPattern := `['"]([a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw)[^'"]*)['"]`
	fse.urlRegex, err = regexp.Compile(urlPattern)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "URL正则编译失败", map[string]interface{}{"error": err.Error()})
	}

	// JWT令牌正则
	fse.jwtRegex, err = regexp.Compile(`['"](ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,})['"]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "JWT正则编译失败", map[string]interface{}{"error": err.Error()})
	}
	
	// 算法检测正则
	fse.algorithmRegex, err = regexp.Compile(`\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|\$\.md5|md5|sha1|sha256|sha512)[\(\.]`)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "算法正则编译失败", map[string]interface{}{"error": err.Error()})
	}
	
	// 初始化nuclei密钥检测正则表达式
	fse.initializeNucleiRegexes()
}

// initializeNucleiRegexes 初始化完整的nuclei密钥检测正则表达式
// 包含708个密钥检测模式，来自findsomething项目的完整nuclei规则
func (fse *FindSomethingExtractor) initializeNucleiRegexes() {
	// 从JSON文件加载完整的nuclei规则
	nucleiPatterns := fse.loadNucleiPatternsFromFile()

	// 如果文件加载失败，记录错误并终止程序
	if len(nucleiPatterns) == 0 {
		fse.logger.Error("findsomething_extractor", "nuclei规则文件加载失败，程序无法继续运行", map[string]interface{}{})
		fse.errorManager.HandleFatalError("nuclei规则文件加载失败", "findsomething_extractor", "initializeNucleiRegexes")
		return
	}

	// 编译所有nuclei正则表达式
	for i, pattern := range nucleiPatterns {
		regex, err := regexp.Compile(pattern)
		if err != nil {
			fse.logger.Error("findsomething_extractor", "nuclei正则编译失败", map[string]interface{}{
				"pattern_index": i,
				"error":         err.Error(),
			})
			continue
		}
		fse.nucleiRegexes = append(fse.nucleiRegexes, regex)
	}

	fse.logger.Info("findsomething_extractor", "完整nuclei正则表达式初始化完成", map[string]interface{}{
		"total_patterns": len(fse.nucleiRegexes),
	})
}

// loadNucleiPatternsFromFile 从JSON文件加载完整的nuclei规则
func (fse *FindSomethingExtractor) loadNucleiPatternsFromFile() []string {
	// 读取nuclei规则文件
	data, err := ioutil.ReadFile("internal/nuclei_patterns.json")
	if err != nil {
		fse.logger.Error("findsomething_extractor", "无法读取nuclei规则文件", map[string]interface{}{
			"error": err.Error(),
		})
		return nil
	}

	var patterns []string
	err = json.Unmarshal(data, &patterns)
	if err != nil {
		fse.logger.Error("findsomething_extractor", "解析nuclei规则文件失败", map[string]interface{}{
			"error": err.Error(),
		})
		return nil
	}

	fse.logger.Info("findsomething_extractor", "从文件加载nuclei规则成功", map[string]interface{}{
		"pattern_count": len(patterns),
	})

	return patterns
}



// ExtractSensitiveInfo 提取敏感信息的主要方法
// 完全基于findsomething项目的extract_info函数实现
func (fse *FindSomethingExtractor) ExtractSensitiveInfo(data string, source string) *SensitiveInfo {
	fse.logger.Debug("findsomething_extractor", "开始提取敏感信息", map[string]interface{}{
		"source":      source,
		"data_length": len(data),
	})

	info := &SensitiveInfo{}

	// 提取身份证号码
	info.SFZ = fse.extractWithRegex(fse.sfzRegex, data, source, "sfz")

	// 提取手机号码
	info.Phone = fse.extractWithRegex(fse.mobileRegex, data, source, "phone")

	// 提取邮箱地址
	info.Mail = fse.extractWithRegex(fse.mailRegex, data, source, "mail")

	// 提取IP地址
	info.IP = fse.extractWithRegex(fse.ipRegex, data, source, "ip")

	// 提取IP:端口
	info.IPPort = fse.extractWithRegex(fse.ipPortRegex, data, source, "ip_port")

	// 提取域名
	info.Domain = fse.extractWithRegex(fse.domainRegex, data, source, "domain")

	// 提取URL
	info.URL = fse.extractWithRegex(fse.urlRegex, data, source, "url")

	// 提取JWT令牌
	info.JWT = fse.extractWithRegex(fse.jwtRegex, data, source, "jwt")

	// 提取算法
	info.Algorithm = fse.extractWithRegex(fse.algorithmRegex, data, source, "algorithm")

	// 提取路径信息（统一归类到path字段）
	completePaths := fse.extractWithRegex(fse.pathRegex, data, source, "path")
	incompletePaths := fse.extractWithRegex(fse.incompletePathRegex, data, source, "path")

	// 合并完整路径和不完整路径，统一保存到path字段
	info.Path = fse.mergePaths(completePaths, incompletePaths)

	// 提取密钥信息（使用nuclei规则）
	info.Secret = fse.extractSecrets(data, source)

	// 从URL中进一步提取IP和域名信息（模拟findsomething的逻辑）
	fse.extractFromURLs(info, data)

	fse.logger.Debug("findsomething_extractor", "敏感信息提取完成", map[string]interface{}{
		"sfz_count":       len(info.SFZ),
		"phone_count":     len(info.Phone),
		"mail_count":      len(info.Mail),
		"ip_count":        len(info.IP),
		"ip_port_count":   len(info.IPPort),
		"domain_count":    len(info.Domain),
		"url_count":       len(info.URL),
		"jwt_count":       len(info.JWT),
		"algorithm_count": len(info.Algorithm),
		"path_count":      len(info.Path),
		"secret_count":    len(info.Secret),
	})

	return info
}

// extractWithRegex 使用正则表达式提取敏感信息的通用方法
func (fse *FindSomethingExtractor) extractWithRegex(regex *regexp.Regexp, data string, source string, infoType string) []InfoItem {
	if regex == nil {
		return nil
	}

	var results []InfoItem
	matches := regex.FindAllStringSubmatch(data, -1)

	for _, match := range matches {
		if len(match) > 1 {
			var value string
			// 对于IP地址和IP端口，需要特殊处理捕获组
			if infoType == "ip" && len(match) > 2 {
				// IP地址在第2个捕获组
				value = fse.cleanValue(match[2])
			} else if infoType == "ip_port" && len(match) > 2 {
				// IP:端口在第2个捕获组
				value = fse.cleanValue(match[2])
			} else {
				// 其他类型使用第1个捕获组
				value = fse.cleanValue(match[1])
			}

			if value != "" && !fse.isDuplicate(results, value) {
				results = append(results, InfoItem{
					Value:  value,
					Source: source,
				})
			}
		}
	}

	return results
}

// extractSecrets 使用nuclei规则提取密钥信息
func (fse *FindSomethingExtractor) extractSecrets(data string, source string) []InfoItem {
	var results []InfoItem

	// 检查nuclei规则是否已加载
	if len(fse.nucleiRegexes) == 0 {
		fse.logger.Debug("findsomething_extractor", "nuclei规则未加载，跳过密钥检测", map[string]interface{}{
			"source": source,
		})
		return results
	}

	// 使用所有nuclei正则表达式进行密钥检测
	for _, regex := range fse.nucleiRegexes {
		matches := regex.FindAllStringSubmatch(data, -1)
		for _, match := range matches {
			if len(match) > 0 {
				// 取整个匹配结果作为密钥
				value := fse.cleanValue(match[0])
				if value != "" && !fse.isDuplicate(results, value) {
					results = append(results, InfoItem{
						Value:  value,
						Source: source,
					})
				}
			}
		}
	}

	return results
}

// mergePaths 合并完整路径和不完整路径
func (fse *FindSomethingExtractor) mergePaths(completePaths, incompletePaths []InfoItem) []InfoItem {
	var results []InfoItem

	// 添加完整路径
	for _, item := range completePaths {
		if !fse.isDuplicate(results, item.Value) {
			results = append(results, item)
		}
	}

	// 添加不完整路径
	for _, item := range incompletePaths {
		if !fse.isDuplicate(results, item.Value) {
			results = append(results, item)
		}
	}

	return results
}

// extractFromURLs 从URL中进一步提取IP和域名信息
// 模拟findsomething项目中extract_info函数的URL处理逻辑
func (fse *FindSomethingExtractor) extractFromURLs(info *SensitiveInfo, data string) {
	if len(info.URL) == 0 {
		return
	}

	// 为每个URL提取IP和域名
	for _, urlItem := range info.URL {
		url := urlItem.Value

		// 从URL中提取IP地址
		if fse.ipRegex != nil {
			ipMatches := fse.ipRegex.FindAllStringSubmatch(url, -1)
			for _, match := range ipMatches {
				if len(match) > 1 {
					value := fse.cleanValue(match[1])
					if value != "" && !fse.isDuplicate(info.IP, value) {
						info.IP = append(info.IP, InfoItem{
							Value:  value,
							Source: urlItem.Source,
						})
					}
				}
			}
		}

		// 从URL中提取IP:端口
		if fse.ipPortRegex != nil {
			ipPortMatches := fse.ipPortRegex.FindAllStringSubmatch(url, -1)
			for _, match := range ipPortMatches {
				if len(match) > 1 {
					value := fse.cleanValue(match[1])
					if value != "" && !fse.isDuplicate(info.IPPort, value) {
						info.IPPort = append(info.IPPort, InfoItem{
							Value:  value,
							Source: urlItem.Source,
						})
					}
				}
			}
		}

		// 从URL中提取域名
		if fse.domainRegex != nil {
			domainMatches := fse.domainRegex.FindAllStringSubmatch(url, -1)
			for _, match := range domainMatches {
				if len(match) > 1 {
					value := fse.cleanValue(match[1])
					if value != "" && !fse.isDuplicate(info.Domain, value) {
						info.Domain = append(info.Domain, InfoItem{
							Value:  value,
							Source: urlItem.Source,
						})
					}
				}
			}
		}
	}
}

// cleanValue 清理提取的值，去除引号和多余的字符
func (fse *FindSomethingExtractor) cleanValue(value string) string {
	// 去除首尾的引号
	value = strings.Trim(value, `"'`)

	// 去除首尾空白字符
	value = strings.TrimSpace(value)

	return value
}

// isDuplicate 检查值是否已经存在于结果中
func (fse *FindSomethingExtractor) isDuplicate(items []InfoItem, value string) bool {
	for _, item := range items {
		if item.Value == value {
			return true
		}
	}
	return false
}

// Close 关闭提取器，释放资源
func (fse *FindSomethingExtractor) Close() {
	fse.logger.Info("findsomething_extractor", "关闭findsomething敏感信息提取器")
}

// MergeSensitiveInfo 合并两个敏感信息结构体
// 参数：
//   - existing: 现有的敏感信息
//   - new: 新的敏感信息
//
// 返回值：
//   - *SensitiveInfo: 合并后的敏感信息
func MergeSensitiveInfo(existing, new *SensitiveInfo) *SensitiveInfo {
	if existing == nil && new == nil {
		return nil
	}

	if existing == nil {
		return new
	}

	if new == nil {
		return existing
	}

	// 创建结果结构体
	result := &SensitiveInfo{}

	// 合并各类敏感信息
	result.SFZ = mergeInfoItems(existing.SFZ, new.SFZ)
	result.Phone = mergeInfoItems(existing.Phone, new.Phone)
	result.Mail = mergeInfoItems(existing.Mail, new.Mail)
	result.IP = mergeInfoItems(existing.IP, new.IP)
	result.IPPort = mergeInfoItems(existing.IPPort, new.IPPort)
	result.Domain = mergeInfoItems(existing.Domain, new.Domain)
	result.URL = mergeInfoItems(existing.URL, new.URL)
	result.Secret = mergeInfoItems(existing.Secret, new.Secret)
	result.Path = mergeInfoItems(existing.Path, new.Path)
	result.JWT = mergeInfoItems(existing.JWT, new.JWT)
	result.Algorithm = mergeInfoItems(existing.Algorithm, new.Algorithm)

	return result
}

// mergeInfoItems 合并信息项列表并去重
func mergeInfoItems(existing, new []InfoItem) []InfoItem {
	if len(existing) == 0 {
		return new
	}

	if len(new) == 0 {
		return existing
	}

	// 使用map进行去重
	seen := make(map[string]bool)
	var result []InfoItem

	// 添加现有项
	for _, item := range existing {
		key := item.Value + "|" + item.Source
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}

	// 添加新项
	for _, item := range new {
		key := item.Value + "|" + item.Source
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}

	return result
}
